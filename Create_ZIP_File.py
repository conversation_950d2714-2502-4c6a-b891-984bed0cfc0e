import zipfile
import os
from datetime import datetime

def create_zip_file():
    """Create ZIP file of the complete project package"""
    
    print("=== CREATING ZIP FILE AUTOMATICALLY ===")
    
    # Set working directory
    base_dir = r'c:\Users\<USER>\Downloads\suhass'
    os.chdir(base_dir)
    
    # Source folder and ZIP file names
    source_folder = "Budesonide_BE_Study_Complete_Package"
    zip_filename = "Budesonide_BE_Study_Complete_Package.zip"
    
    # Check if source folder exists
    if not os.path.exists(source_folder):
        print(f"❌ Error: Source folder '{source_folder}' not found!")
        return False
    
    # Remove existing ZIP file if it exists
    if os.path.exists(zip_filename):
        os.remove(zip_filename)
        print(f"✓ Removed existing ZIP file")
    
    # Create ZIP file
    try:
        with zipfile.ZipFile(zip_filename, 'w', zipfile.ZIP_DEFLATED) as zipf:
            print(f"✓ Creating ZIP file: {zip_filename}")
            
            # Walk through all files and folders
            files_added = 0
            for root, dirs, files in os.walk(source_folder):
                for file in files:
                    file_path = os.path.join(root, file)
                    # Create archive path (relative to source folder)
                    archive_path = os.path.relpath(file_path, base_dir)
                    
                    # Add file to ZIP
                    zipf.write(file_path, archive_path)
                    files_added += 1
                    print(f"  ✓ Added: {archive_path}")
        
        # Verify ZIP file was created
        if os.path.exists(zip_filename):
            zip_size = os.path.getsize(zip_filename)
            zip_size_mb = zip_size / (1024 * 1024)
            
            print(f"\n🎉 ZIP FILE CREATED SUCCESSFULLY!")
            print(f"   📁 File: {zip_filename}")
            print(f"   📊 Size: {zip_size_mb:.2f} MB ({zip_size:,} bytes)")
            print(f"   📄 Files: {files_added} files included")
            print(f"   📍 Location: {os.path.abspath(zip_filename)}")
            
            # Test ZIP file integrity
            try:
                with zipfile.ZipFile(zip_filename, 'r') as test_zip:
                    test_result = test_zip.testzip()
                    if test_result is None:
                        print(f"   ✅ ZIP integrity: VERIFIED")
                    else:
                        print(f"   ⚠️  ZIP integrity: Issue with {test_result}")
            except Exception as e:
                print(f"   ⚠️  ZIP test failed: {e}")
            
            return True
            
    except Exception as e:
        print(f"❌ Error creating ZIP file: {e}")
        return False

def list_zip_contents():
    """List contents of the created ZIP file"""
    
    zip_filename = "Budesonide_BE_Study_Complete_Package.zip"
    
    if not os.path.exists(zip_filename):
        print(f"❌ ZIP file not found: {zip_filename}")
        return
    
    print(f"\n📋 ZIP FILE CONTENTS:")
    print(f"=" * 50)
    
    try:
        with zipfile.ZipFile(zip_filename, 'r') as zipf:
            file_list = zipf.namelist()
            
            # Organize by folders
            folders = {}
            for file_path in file_list:
                if '/' in file_path:
                    folder = file_path.split('/')[1]  # Get first subfolder
                    if folder not in folders:
                        folders[folder] = []
                    folders[folder].append(file_path)
                else:
                    if 'root' not in folders:
                        folders['root'] = []
                    folders['root'].append(file_path)
            
            # Display organized contents
            for folder, files in sorted(folders.items()):
                if folder == 'root':
                    print(f"\n📁 Root Files:")
                else:
                    print(f"\n📁 {folder}/")
                
                for file_path in sorted(files):
                    filename = os.path.basename(file_path)
                    print(f"   📄 {filename}")
            
            print(f"\n📊 SUMMARY:")
            print(f"   Total files: {len(file_list)}")
            print(f"   Folders: {len([f for f in folders.keys() if f != 'root'])}")
            
    except Exception as e:
        print(f"❌ Error reading ZIP contents: {e}")

def create_sharing_instructions():
    """Create instructions for sharing the ZIP file"""
    
    instructions = f"""
SHARING INSTRUCTIONS FOR ZIP FILE
=================================

Created: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
File: Budesonide_BE_Study_Complete_Package.zip

HOW TO SHARE:
============

📧 EMAIL SHARING:
- Attach the ZIP file to your email
- File size is small (~0.1-0.5 MB) - no issues with email limits
- Recipients can download and extract anywhere

☁️ CLOUD SHARING:
- Upload to Google Drive, Dropbox, OneDrive, etc.
- Share the download link
- Recipients get the complete package

💾 USB/PHYSICAL SHARING:
- Copy ZIP file to USB drive
- Give USB to recipients
- They can copy and extract on their computers

📱 MESSAGING APPS:
- Send via WhatsApp, Telegram, Slack, etc.
- Small file size makes it easy to share

RECIPIENT INSTRUCTIONS:
======================

1. EXTRACT ZIP FILE:
   - Right-click ZIP file → "Extract All" (Windows)
   - Double-click ZIP file (Mac)
   - Use any unzip utility (Linux)

2. START HERE:
   - Open extracted folder
   - Read: README_Project_Overview.md
   - Follow the instructions step-by-step

3. FOR JASP ANALYSIS:
   - Go to: 03_JASP_Analysis/ folder
   - Follow: Complete_JASP_Analysis_Guide.md
   - Install JASP (free): https://jasp-stats.org/download/

4. FOR FDA SUBMISSION:
   - Go to: 04_FDA_Submission/ folder
   - Use the eCTD format documents
   - Convert to PDF as instructed

SYSTEM REQUIREMENTS:
===================
✅ Any computer (Windows, Mac, Linux)
✅ Standard office software (Excel, Word, etc.)
✅ JASP software (free download)
✅ No special licenses or hardware needed

WHAT'S INCLUDED:
===============
✅ Complete bioequivalence study
✅ FDA PSG compliant analysis
✅ JASP analysis guide (12 steps)
✅ FDA submission documents
✅ All data files and results
✅ Professional documentation

SUPPORT:
========
- Each folder has README.md with instructions
- JASP guide is step-by-step with screenshots
- All files are standard formats
- Cross-platform compatibility guaranteed

The ZIP file contains everything needed for complete 
bioequivalence analysis and FDA submission!
"""
    
    with open("SHARING_INSTRUCTIONS.txt", 'w', encoding='utf-8') as f:
        f.write(instructions)
    
    print(f"✓ Created sharing instructions: SHARING_INSTRUCTIONS.txt")

if __name__ == "__main__":
    # Create ZIP file
    success = create_zip_file()
    
    if success:
        # List contents
        list_zip_contents()
        
        # Create sharing instructions
        create_sharing_instructions()
        
        print(f"\n🎯 READY TO SHARE!")
        print(f"   ✅ ZIP file created and verified")
        print(f"   ✅ Contents listed and organized")
        print(f"   ✅ Sharing instructions provided")
        print(f"   ✅ Cross-platform compatibility ensured")
        
        print(f"\n📧 YOU CAN NOW SHARE:")
        print(f"   📁 Budesonide_BE_Study_Complete_Package.zip")
        print(f"   📋 SHARING_INSTRUCTIONS.txt (optional)")
        
        print(f"\n🎉 PROJECT PACKAGE COMPLETE AND READY!")
    else:
        print(f"\n❌ Failed to create ZIP file. Please try manual creation.")
