import pandas as pd
import numpy as np
import os
from scipy import stats
from scipy.stats import t, f
import warnings
warnings.filterwarnings('ignore')

def extract_and_clean_data():
    """Extract and clean PBE data from Excel file"""
    try:
        # Read the Excel file
        df = pd.read_excel('PBE Data Packet.xlsx', header=None)
        
        print("=== COMPREHENSIVE PBE DATA EXTRACTION ===")
        
        # Find the actual data by looking for patterns
        ref_data = []
        test_data = []
        
        # Extract Reference and Test data
        for idx, row in df.iterrows():
            row_str = ' '.join([str(x) for x in row if pd.notna(x)])
            
            # Look for REF data with batch numbers and mean values
            if 'REF' in row_str and any(batch in row_str for batch in ['REF 1', 'REF 2', 'REF 3']):
                values = [x for x in row if pd.notna(x)]
                if len(values) >= 6:  # Ensure we have enough data
                    try:
                        mean_val = float(values[5]) if str(values[5]).replace('.', '').isdigit() else None
                        if mean_val:
                            ref_data.append({
                                'Product': 'REF',
                                'Batch': values[2],
                                'Mean_Value': mean_val,
                                'Log_Mean': np.log(mean_val)
                            })
                    except:
                        pass
            
            # Look for TEST data
            elif 'TEST' in row_str and any(batch in row_str for batch in ['TEST 1', 'TEST 2', 'TEST 3']):
                values = [x for x in row if pd.notna(x)]
                if len(values) >= 6:
                    try:
                        mean_val = float(values[5]) if str(values[5]).replace('.', '').isdigit() else None
                        if mean_val:
                            test_data.append({
                                'Product': 'TEST',
                                'Batch': values[2],
                                'Mean_Value': mean_val,
                                'Log_Mean': np.log(mean_val)
                            })
                    except:
                        pass
        
        # Create DataFrames
        ref_df = pd.DataFrame(ref_data)
        test_df = pd.DataFrame(test_data)
        combined_df = pd.concat([ref_df, test_df], ignore_index=True)
        
        print(f"Reference batches found: {len(ref_df)}")
        print(f"Test batches found: {len(test_df)}")
        print(f"\nReference data:")
        print(ref_df)
        print(f"\nTest data:")
        print(test_df)
        
        # Save data
        combined_df.to_csv('pbe_clean_data.csv', index=False)
        
        return ref_df, test_df, combined_df
        
    except Exception as e:
        print(f"Error extracting data: {e}")
        return None, None, None

def calculate_pbe_statistics(ref_df, test_df):
    """Calculate Population Bioequivalence Statistics"""
    if ref_df is None or test_df is None or ref_df.empty or test_df.empty:
        print("Insufficient data for PBE analysis")
        return None
    
    print("\n=== POPULATION BIOEQUIVALENCE ANALYSIS ===")
    
    # Basic statistics
    ref_means = ref_df['Mean_Value'].values
    test_means = test_df['Mean_Value'].values
    ref_log_means = ref_df['Log_Mean'].values
    test_log_means = test_df['Log_Mean'].values
    
    # Calculate geometric means
    ref_geom_mean = np.exp(np.mean(ref_log_means))
    test_geom_mean = np.exp(np.mean(test_log_means))
    
    # Calculate ratio
    ratio = test_geom_mean / ref_geom_mean
    log_ratio = np.log(ratio)
    
    # Calculate variances
    ref_var = np.var(ref_log_means, ddof=1)
    test_var = np.var(test_log_means, ddof=1)
    
    # Calculate pooled variance (MSW)
    n_ref = len(ref_means)
    n_test = len(test_means)
    pooled_var = ((n_ref - 1) * ref_var + (n_test - 1) * test_var) / (n_ref + n_test - 2)
    
    # PBE calculations
    # Standard PBE criterion: θ = (μT - μR)² + σ²TT - σ²TR
    mean_diff_sq = (np.mean(test_log_means) - np.mean(ref_log_means))**2
    pbe_criterion = mean_diff_sq + test_var - ref_var
    
    # PBE constant (typically 0.05 for 20% difference)
    pbe_constant = (np.log(1.25))**2  # For 25% difference
    
    # Confidence interval for ratio
    se_ratio = np.sqrt(pooled_var * (1/n_ref + 1/n_test))
    df = n_ref + n_test - 2
    t_critical = t.ppf(0.975, df)  # 95% CI
    
    ci_lower = np.exp(log_ratio - t_critical * se_ratio)
    ci_upper = np.exp(log_ratio + t_critical * se_ratio)
    
    # TOST (Two One-Sided Tests) for Average Bioequivalence
    # H0: |log(ratio)| >= log(1.25) vs H1: |log(ratio)| < log(1.25)
    theta_limit = np.log(1.25)  # 25% limit
    
    # Test 1: H0: log(ratio) >= theta vs H1: log(ratio) < theta
    t1 = (log_ratio - theta_limit) / se_ratio
    p1 = t.cdf(t1, df)
    
    # Test 2: H0: log(ratio) <= -theta vs H1: log(ratio) > -theta
    t2 = (log_ratio + theta_limit) / se_ratio
    p2 = 1 - t.cdf(t2, df)
    
    # TOST p-value is the maximum of the two one-sided p-values
    tost_p_value = max(p1, p2)
    
    results = {
        'Reference_Geometric_Mean': ref_geom_mean,
        'Test_Geometric_Mean': test_geom_mean,
        'Geometric_Mean_Ratio': ratio,
        'Log_Ratio': log_ratio,
        'Ratio_90_CI_Lower': np.exp(log_ratio - t.ppf(0.95, df) * se_ratio),
        'Ratio_90_CI_Upper': np.exp(log_ratio + t.ppf(0.95, df) * se_ratio),
        'Ratio_95_CI_Lower': ci_lower,
        'Ratio_95_CI_Upper': ci_upper,
        'Reference_Variance': ref_var,
        'Test_Variance': test_var,
        'Pooled_Variance': pooled_var,
        'PBE_Criterion': pbe_criterion,
        'PBE_Constant': pbe_constant,
        'PBE_Pass': pbe_criterion <= pbe_constant,
        'TOST_T1': t1,
        'TOST_T2': t2,
        'TOST_P_Value': tost_p_value,
        'TOST_Pass': tost_p_value <= 0.05,
        'Average_BE_Pass': (ci_lower >= 0.8) and (ci_upper <= 1.25),
        'Degrees_Freedom': df,
        'Standard_Error': se_ratio
    }
    
    return results

def print_results(results):
    """Print comprehensive results"""
    if results is None:
        return
    
    print("\n" + "="*60)
    print("POPULATION BIOEQUIVALENCE ANALYSIS RESULTS")
    print("="*60)
    
    print(f"\n📊 GEOMETRIC MEANS:")
    print(f"   Reference: {results['Reference_Geometric_Mean']:.4f}")
    print(f"   Test:      {results['Test_Geometric_Mean']:.4f}")
    print(f"   Ratio:     {results['Geometric_Mean_Ratio']:.4f}")
    
    print(f"\n📈 CONFIDENCE INTERVALS:")
    print(f"   90% CI: [{results['Ratio_90_CI_Lower']:.4f}, {results['Ratio_90_CI_Upper']:.4f}]")
    print(f"   95% CI: [{results['Ratio_95_CI_Lower']:.4f}, {results['Ratio_95_CI_Upper']:.4f}]")
    
    print(f"\n🔬 VARIANCE ANALYSIS:")
    print(f"   Reference Variance: {results['Reference_Variance']:.6f}")
    print(f"   Test Variance:      {results['Test_Variance']:.6f}")
    print(f"   Pooled Variance:    {results['Pooled_Variance']:.6f}")
    
    print(f"\n🎯 POPULATION BIOEQUIVALENCE:")
    print(f"   PBE Criterion: {results['PBE_Criterion']:.6f}")
    print(f"   PBE Constant:  {results['PBE_Constant']:.6f}")
    print(f"   PBE Result:    {'✅ PASS' if results['PBE_Pass'] else '❌ FAIL'}")
    
    print(f"\n📋 TOST ANALYSIS:")
    print(f"   TOST P-value: {results['TOST_P_Value']:.6f}")
    print(f"   TOST Result:  {'✅ PASS' if results['TOST_Pass'] else '❌ FAIL'}")
    
    print(f"\n⚖️ AVERAGE BIOEQUIVALENCE:")
    print(f"   ABE Result: {'✅ PASS' if results['Average_BE_Pass'] else '❌ FAIL'}")
    print(f"   (90% CI within 80.00% - 125.00%)")
    
    print(f"\n📊 STATISTICAL PARAMETERS:")
    print(f"   Degrees of Freedom: {results['Degrees_Freedom']}")
    print(f"   Standard Error:     {results['Standard_Error']:.6f}")

if __name__ == "__main__":
    # Change to the correct directory
    os.chdir(r'c:\Users\<USER>\Downloads\suhass')
    
    # Extract and clean data
    ref_df, test_df, combined_df = extract_and_clean_data()
    
    # Calculate PBE statistics
    results = calculate_pbe_statistics(ref_df, test_df)
    
    # Print results
    print_results(results)
    
    # Save results to file
    if results:
        results_df = pd.DataFrame([results])
        results_df.to_csv('pbe_analysis_results.csv', index=False)
        print(f"\n💾 Results saved to 'pbe_analysis_results.csv'")
    
    print(f"\n📁 Files created:")
    print(f"   1. pbe_clean_data.csv - Cleaned dataset")
    print(f"   2. pbe_analysis_results.csv - Analysis results")
    print(f"\n🔧 For JASP analysis:")
    print(f"   1. Download JASP from: https://jasp-stats.org/download/")
    print(f"   2. Open pbe_clean_data.csv in JASP")
    print(f"   3. Use T-Tests > Paired Samples T-Test for TOST analysis")
    print(f"   4. Use ANOVA > Repeated Measures ANOVA for detailed analysis")
