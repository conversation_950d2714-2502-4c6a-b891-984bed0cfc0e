import pandas as pd
import numpy as np
import os
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

def extract_pbe_data():
    """Extract data from PBE Data Packet.xlsx"""
    try:
        # Read the Excel file with proper header row
        df = pd.read_excel('PBE Data Packet.xlsx', header=1)

        # Remove completely empty rows and columns
        df = df.dropna(how='all').dropna(axis=1, how='all')

        print("=== PBE DATA EXTRACTION ===")
        print(f"Data shape: {df.shape}")
        print(f"\nColumn names: {df.columns.tolist()}")
        print(f"\nFirst 10 rows:")
        print(df.head(10))
        print(f"\nData types:")
        print(df.dtypes)

        # Clean column names
        df.columns = [str(col).strip() for col in df.columns]

        # Show non-null data statistics
        print(f"\nNon-null counts per column:")
        print(df.count())

        # Show unique values in categorical columns
        for col in df.columns:
            if df[col].dtype == 'object':
                unique_vals = df[col].dropna().unique()
                if len(unique_vals) <= 20:  # Only show if reasonable number
                    print(f"\nUnique values in '{col}': {unique_vals}")

        print(f"\nBasic statistics for numeric columns:")
        numeric_df = df.select_dtypes(include=[np.number])
        if not numeric_df.empty:
            print(numeric_df.describe())

        # Save as CSV for JASP
        df.to_csv('pbe_data_for_jasp.csv', index=False)
        print(f"\nData saved as 'pbe_data_for_jasp.csv' for JASP analysis")

        return df

    except Exception as e:
        print(f"Error reading Excel file: {e}")
        return None

def perform_preliminary_analysis(df):
    """Perform preliminary statistical analysis"""
    if df is None:
        return

    print("\n=== PRELIMINARY STATISTICAL ANALYSIS ===")

    # Identify potential bioequivalence variables
    numeric_cols = df.select_dtypes(include=[np.number]).columns
    print(f"Numeric columns: {numeric_cols.tolist()}")

    # Look for typical bioequivalence study variables
    potential_vars = []
    for col in df.columns:
        col_lower = col.lower()
        if any(keyword in col_lower for keyword in ['auc', 'cmax', 'tmax', 'concentration', 'plasma', 'pk']):
            potential_vars.append(col)

    if potential_vars:
        print(f"Potential PK variables identified: {potential_vars}")

    # Check for treatment/formulation variables
    treatment_vars = []
    for col in df.columns:
        col_lower = col.lower()
        if any(keyword in col_lower for keyword in ['treatment', 'formulation', 'period', 'sequence', 'subject']):
            treatment_vars.append(col)

    if treatment_vars:
        print(f"Potential design variables: {treatment_vars}")

if __name__ == "__main__":
    # Change to the correct directory
    os.chdir(r'c:\Users\<USER>\Downloads\suhass')

    # Extract data
    df = extract_pbe_data()

    # Perform preliminary analysis
    perform_preliminary_analysis(df)
