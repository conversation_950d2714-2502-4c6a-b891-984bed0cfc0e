import pandas as pd
import numpy as np
import os
from scipy import stats
from scipy.stats import t
import warnings
warnings.filterwarnings('ignore')

def clean_and_structure_data():
    """Clean and structure the PBE data for analysis"""
    try:
        # Read the Excel file with proper header row
        df = pd.read_excel('PBE Data Packet.xlsx', header=1)
        
        # Remove completely empty rows and columns
        df = df.dropna(how='all').dropna(axis=1, how='all')
        
        print("=== STRUCTURING PBE DATA FOR ANALYSIS ===")
        
        # Extract the actual data rows (excluding summary rows)
        data_rows = []
        
        for idx, row in df.iterrows():
            # Look for rows with actual data (Sr No, Product, Batch Number, etc.)
            if (pd.notna(row.iloc[0]) and 
                str(row.iloc[0]).isdigit() and 
                row.iloc[1] in ['REF', 'TEST']):
                
                data_dict = {
                    'Sr_No': int(row.iloc[0]),
                    'Product': row.iloc[1],
                    'Batch_Number': row.iloc[2],
                    'Iteration': row.iloc[3] if pd.notna(row.iloc[3]) else 1,
                    'Mean_Value': row.iloc[5] if pd.notna(row.iloc[5]) else None
                }
                
                # Extract additional numeric values if present
                for i, val in enumerate(row.iloc[6:], start=6):
                    if pd.notna(val) and str(val).replace('.', '').replace('-', '').isdigit():
                        try:
                            data_dict[f'Value_{i}'] = float(val)
                        except:
                            pass
                
                data_rows.append(data_dict)
        
        # Create structured DataFrame
        structured_df = pd.DataFrame(data_rows)
        
        print(f"Structured data shape: {structured_df.shape}")
        print(f"\nStructured data columns: {structured_df.columns.tolist()}")
        print(f"\nFirst 10 rows of structured data:")
        print(structured_df.head(10))
        
        # Save structured data
        structured_df.to_csv('pbe_structured_data.csv', index=False)
        print(f"\nStructured data saved as 'pbe_structured_data.csv'")
        
        return structured_df
        
    except Exception as e:
        print(f"Error structuring data: {e}")
        return None

def extract_summary_statistics():
    """Extract summary statistics from the Excel file"""
    try:
        df = pd.read_excel('PBE Data Packet.xlsx', header=1)
        df = df.dropna(how='all').dropna(axis=1, how='all')
        
        print("\n=== EXTRACTING SUMMARY STATISTICS ===")
        
        summary_stats = {}
        
        # Look for key statistical values
        for idx, row in df.iterrows():
            row_str = ' '.join([str(x) for x in row if pd.notna(x)])
            
            # Extract geometric means
            if 'Geometric Mean Reference' in row_str:
                try:
                    summary_stats['Geometric_Mean_Reference'] = float(row.iloc[4])
                except:
                    pass
            
            if 'Geometric Mean Test' in row_str:
                try:
                    summary_stats['Geometric_Mean_Test'] = float(row.iloc[1])
                except:
                    pass
            
            # Extract ratio
            if 'Geometric Mean Ratio' in row_str:
                try:
                    summary_stats['Geometric_Mean_Ratio'] = float(row.iloc[4])
                except:
                    pass
            
            # Extract confidence bounds
            if '95% Upper Confidence Bound' in row_str:
                try:
                    summary_stats['Upper_Confidence_Bound'] = float(row.iloc[5])
                except:
                    pass
            
            # Extract PBE constant
            if 'PBE Constant' in row_str:
                try:
                    summary_stats['PBE_Constant'] = float(row.iloc[3])
                except:
                    pass
        
        print("Summary Statistics Found:")
        for key, value in summary_stats.items():
            print(f"{key}: {value}")
        
        return summary_stats
        
    except Exception as e:
        print(f"Error extracting summary statistics: {e}")
        return {}

def perform_pbe_analysis(structured_df, summary_stats):
    """Perform Population Bioequivalence Analysis"""
    if structured_df is None or structured_df.empty:
        print("No structured data available for PBE analysis")
        return
    
    print("\n=== POPULATION BIOEQUIVALENCE ANALYSIS ===")
    
    # Separate Reference and Test data
    ref_data = structured_df[structured_df['Product'] == 'REF']
    test_data = structured_df[structured_df['Product'] == 'TEST']
    
    print(f"Reference data points: {len(ref_data)}")
    print(f"Test data points: {len(test_data)}")
    
    # Calculate basic statistics for Mean_Value if available
    if 'Mean_Value' in structured_df.columns:
        ref_values = pd.to_numeric(ref_data['Mean_Value'], errors='coerce').dropna()
        test_values = pd.to_numeric(test_data['Mean_Value'], errors='coerce').dropna()
        
        if len(ref_values) > 0 and len(test_values) > 0:
            print(f"\nReference Mean Values Statistics:")
            print(f"  Count: {len(ref_values)}")
            print(f"  Mean: {ref_values.mean():.4f}")
            print(f"  Std Dev: {ref_values.std():.4f}")
            print(f"  CV%: {(ref_values.std()/ref_values.mean()*100):.2f}%")
            
            print(f"\nTest Mean Values Statistics:")
            print(f"  Count: {len(test_values)}")
            print(f"  Mean: {test_values.mean():.4f}")
            print(f"  Std Dev: {test_values.std():.4f}")
            print(f"  CV%: {(test_values.std()/test_values.mean()*100):.2f}%")
            
            # Calculate ratio
            ratio = test_values.mean() / ref_values.mean()
            print(f"\nTest/Reference Ratio: {ratio:.4f}")
    
    # Display extracted summary statistics
    if summary_stats:
        print(f"\n=== EXTRACTED SUMMARY STATISTICS ===")
        for key, value in summary_stats.items():
            print(f"{key}: {value}")
    
    return {
        'ref_data': ref_data,
        'test_data': test_data,
        'summary_stats': summary_stats
    }

def create_jasp_ready_data(structured_df):
    """Create JASP-ready dataset"""
    if structured_df is None or structured_df.empty:
        return
    
    print("\n=== CREATING JASP-READY DATASET ===")
    
    # Create a clean dataset for JASP
    jasp_data = structured_df.copy()
    
    # Convert numeric columns
    numeric_cols = ['Mean_Value'] + [col for col in jasp_data.columns if col.startswith('Value_')]
    for col in numeric_cols:
        if col in jasp_data.columns:
            jasp_data[col] = pd.to_numeric(jasp_data[col], errors='coerce')
    
    # Create treatment factor
    jasp_data['Treatment'] = jasp_data['Product'].map({'REF': 0, 'TEST': 1})
    
    # Save for JASP
    jasp_data.to_csv('pbe_data_for_jasp.csv', index=False)
    print(f"JASP-ready data saved as 'pbe_data_for_jasp.csv'")
    print(f"Data shape: {jasp_data.shape}")
    print(f"Columns: {jasp_data.columns.tolist()}")
    
    return jasp_data

if __name__ == "__main__":
    # Change to the correct directory
    os.chdir(r'c:\Users\<USER>\Downloads\suhass')
    
    # Clean and structure data
    structured_df = clean_and_structure_data()
    
    # Extract summary statistics
    summary_stats = extract_summary_statistics()
    
    # Perform PBE analysis
    analysis_results = perform_pbe_analysis(structured_df, summary_stats)
    
    # Create JASP-ready data
    jasp_data = create_jasp_ready_data(structured_df)
    
    print("\n=== ANALYSIS COMPLETE ===")
    print("Files created:")
    print("1. pbe_structured_data.csv - Cleaned and structured data")
    print("2. pbe_data_for_jasp.csv - JASP-ready dataset")
