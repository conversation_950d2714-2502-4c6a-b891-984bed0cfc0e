# JASP INSTALLATION AND SETU<PERSON> GUIDE
## For Bioequivalence Analysis

---

## 📥 **STEP 1: DOWNLOAD JASP**

### **Official Website:**
**https://jasp-stats.org/download/**

### **Choose Your Operating System:**

#### **Windows:**
- Click **"Download JASP for Windows"**
- File: `JASP-[version]-Windows.exe`
- Size: ~200MB
- Requirements: Windows 10 or later

#### **Mac:**
- Click **"Download JASP for macOS"**
- File: `JASP-[version]-macOS.dmg`
- Size: ~200MB
- Requirements: macOS 10.15 or later

#### **Linux:**
- Click **"Download JASP for Linux"**
- File: `JASP-[version]-Linux.flatpak`
- Size: ~200MB
- Requirements: Ubuntu 18.04 or equivalent

---

## 🔧 **STEP 2: INSTALL JASP**

### **Windows Installation:**
1. **Run the installer**: Double-click `JASP-[version]-Windows.exe`
2. **Follow setup wizard**: Click "Next" through installation steps
3. **Choose installation folder**: Default location is recommended
4. **Create desktop shortcut**: Check the box if desired
5. **Complete installation**: Click "Finish"

### **Mac Installation:**
1. **Open DMG file**: Double-click `JASP-[version]-macOS.dmg`
2. **Drag JASP to Applications**: Drag JASP icon to Applications folder
3. **Security settings**: May need to allow in System Preferences → Security
4. **Launch JASP**: Double-click JASP in Applications folder

### **Linux Installation:**
1. **Install Flatpak** (if not installed): `sudo apt install flatpak`
2. **Install JASP**: `flatpak install --user JASP-[version]-Linux.flatpak`
3. **Launch JASP**: `flatpak run org.jasp.JASP`

---

## 🚀 **STEP 3: FIRST LAUNCH**

### **Launch JASP:**
1. **Windows**: Click JASP icon on desktop or Start menu
2. **Mac**: Click JASP in Applications folder
3. **Linux**: Use flatpak command or application menu

### **First Time Setup:**
1. **Welcome screen**: JASP will show welcome message
2. **No registration required**: JASP is completely free
3. **Interface tour**: Optional guided tour available
4. **Ready to use**: No additional setup needed

---

## 📊 **STEP 4: VERIFY INSTALLATION**

### **Test JASP Installation:**
1. **Open JASP**
2. **Click "Open" → "Examples"**
3. **Select any example dataset**
4. **Try running a simple analysis**
5. **If successful**: Installation is complete ✅

### **Check Version:**
- **Help** → **About JASP**
- Recommended: Version 0.17 or later
- All versions support bioequivalence analysis

---

## 📁 **STEP 5: PREPARE FOR BIOEQUIVALENCE ANALYSIS**

### **Required Files (Included in Project):**
1. **`JASP_Data_Format.csv`** - Your bioequivalence data
2. **`Complete_JASP_Analysis_Guide.md`** - Step-by-step instructions
3. **`JASP_Results_Template.md`** - Results format template

### **File Locations:**
- **Data file**: `03_JASP_Analysis/JASP_Data_Format.csv`
- **Instructions**: `03_JASP_Analysis/Complete_JASP_Analysis_Guide.md`
- **Template**: `02_Main_Reports/JASP_Results_Template.md`

---

## 🎯 **STEP 6: LOAD YOUR DATA**

### **Quick Start:**
1. **Open JASP**
2. **Click "Open" → "Computer" → "Browse"**
3. **Navigate to project folder**
4. **Select `03_JASP_Analysis/JASP_Data_Format.csv`**
5. **Click "Open"**

### **Verify Data Loading:**
You should see:
- **7 rows** of data (4 Reference + 3 Test)
- **5 columns**: Batch_ID, Product, Release_Parameter, Log_Release, Group_Code
- **No missing values**
- **Proper data types** (numbers and text)

---

## 📋 **STEP 7: JASP INTERFACE OVERVIEW**

### **Main Areas:**
1. **Data View**: Shows your loaded data (bottom panel)
2. **Analysis Menu**: Statistical analyses (top ribbon)
3. **Results Panel**: Analysis outputs (right panel)
4. **Options Panel**: Analysis settings (left panel when analysis selected)

### **Key Menus for Bioequivalence:**
- **Descriptives**: For basic statistics
- **T-Tests**: For group comparisons
- **Frequencies**: For power analysis
- **File**: For saving and exporting

---

## 🔧 **TROUBLESHOOTING**

### **Common Issues:**

#### **Installation Problems:**
- **Windows**: Run as administrator
- **Mac**: Check security settings in System Preferences
- **Linux**: Ensure Flatpak is properly installed

#### **Data Loading Issues:**
- **CSV format**: Ensure file is properly formatted CSV
- **File path**: Check file location and permissions
- **Data encoding**: Use UTF-8 encoding for special characters

#### **Analysis Problems:**
- **Missing values**: Check data completeness
- **Variable types**: Ensure numeric data is recognized as numbers
- **Sample size**: Verify adequate data for analysis

### **Solutions:**
1. **Restart JASP**: Close and reopen the application
2. **Check file format**: Ensure CSV is properly formatted
3. **Verify data**: Check data in Excel before importing
4. **Update JASP**: Download latest version if issues persist

---

## 📞 **SUPPORT RESOURCES**

### **Official JASP Support:**
- **Website**: https://jasp-stats.org/
- **Documentation**: https://jasp-stats.org/how-to-use-jasp/
- **Video tutorials**: Available on JASP website
- **Community forum**: https://forum.cogsci.nl/

### **For This Project:**
- **Follow**: `Complete_JASP_Analysis_Guide.md` for detailed steps
- **Use**: `JASP_Results_Template.md` for formatting results
- **Reference**: FDA PSG guidance for methodology questions

---

## ✅ **INSTALLATION CHECKLIST**

### **Pre-Analysis Checklist:**
- [ ] **JASP downloaded** from official website
- [ ] **JASP installed** successfully on your system
- [ ] **JASP launches** without errors
- [ ] **Data file located**: `JASP_Data_Format.csv` found
- [ ] **Instructions ready**: `Complete_JASP_Analysis_Guide.md` available
- [ ] **Template prepared**: `JASP_Results_Template.md` ready

### **Ready for Analysis:**
- [ ] **JASP running** properly
- [ ] **Data loaded** successfully (7 records visible)
- [ ] **Variables recognized** correctly (5 columns)
- [ ] **Analysis guide** open and ready to follow
- [ ] **Results template** ready for completion

---

## 🎉 **YOU'RE READY!**

### **Next Steps:**
1. **Open**: `Complete_JASP_Analysis_Guide.md`
2. **Follow**: All 12 steps for complete analysis
3. **Generate**: Professional bioequivalence results
4. **Export**: FDA-ready statistical report

### **Expected Timeline:**
- **JASP Installation**: 10-15 minutes
- **Data Loading**: 2-3 minutes
- **Complete Analysis**: 1-2 hours (following guide)
- **Results Generation**: 15-30 minutes

**You now have everything needed to complete your FDA PSG bioequivalence analysis in JASP!** 🎯✅

---

**Installation Guide Version**: 1.0  
**Compatible JASP Versions**: 0.16 and later  
**Last Updated**: December 2024  
**Status**: Ready for bioequivalence analysis
