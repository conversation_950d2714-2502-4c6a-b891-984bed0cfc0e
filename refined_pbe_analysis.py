import pandas as pd
import numpy as np
import os
from scipy import stats
from scipy.stats import t, f
import warnings
warnings.filterwarnings('ignore')

def extract_refined_data():
    """Extract refined PBE data with better filtering"""
    try:
        # Read the Excel file
        df = pd.read_excel('PBE Data Packet.xlsx', header=None)
        
        print("=== REFINED PBE DATA EXTRACTION ===")
        
        # Extract data more carefully
        ref_data = []
        test_data = []
        
        # Look for the actual mean values in the expected range (15-25 for this type of data)
        for idx, row in df.iterrows():
            row_str = ' '.join([str(x) for x in row if pd.notna(x)])
            
            # Extract REF data
            if 'REF' in row_str and any(batch in row_str for batch in ['REF 1', 'REF 2', 'REF 3']):
                values = [x for x in row if pd.notna(x)]
                if len(values) >= 6:
                    try:
                        mean_val = float(values[5])
                        # Filter for reasonable mean values (15-25 range)
                        if 15 <= mean_val <= 25:
                            batch_num = values[2]
                            ref_data.append({
                                'Product': 'REF',
                                'Batch': batch_num,
                                'Mean_Value': mean_val,
                                'Log_Mean': np.log(mean_val)
                            })
                    except:
                        pass
            
            # Extract TEST data
            elif 'TEST' in row_str and any(batch in row_str for batch in ['TEST 1', 'TEST 2', 'TEST 3']):
                values = [x for x in row if pd.notna(x)]
                if len(values) >= 6:
                    try:
                        mean_val = float(values[5])
                        # Filter for reasonable mean values
                        if 15 <= mean_val <= 25:
                            batch_num = values[2]
                            test_data.append({
                                'Product': 'TEST',
                                'Batch': batch_num,
                                'Mean_Value': mean_val,
                                'Log_Mean': np.log(mean_val)
                            })
                    except:
                        pass
        
        # Remove duplicates and create DataFrames
        ref_df = pd.DataFrame(ref_data).drop_duplicates()
        test_df = pd.DataFrame(test_data).drop_duplicates()
        
        print(f"Reference batches found: {len(ref_df)}")
        print(f"Test batches found: {len(test_df)}")
        print(f"\nReference data:")
        print(ref_df)
        print(f"\nTest data:")
        print(test_df)
        
        # If we don't have enough data, try alternative extraction
        if len(ref_df) < 3 or len(test_df) < 3:
            print("\nTrying alternative data extraction...")
            ref_df, test_df = extract_from_summary_table(df)
        
        return ref_df, test_df
        
    except Exception as e:
        print(f"Error extracting data: {e}")
        return None, None

def extract_from_summary_table(df):
    """Extract data from summary statistics table"""
    ref_data = []
    test_data = []
    
    # Look for summary statistics in the data
    for idx, row in df.iterrows():
        row_str = ' '.join([str(x) for x in row if pd.notna(x)])
        
        # Look for batch means in summary table
        if 'Mean 1' in row_str or 'Mean 2' in row_str or 'Mean 3' in row_str:
            values = [x for x in row if pd.notna(x)]
            try:
                if len(values) >= 3:
                    ref_val = float(values[1]) if str(values[1]).replace('.', '').isdigit() else None
                    test_val = float(values[2]) if str(values[2]).replace('.', '').isdigit() else None
                    
                    if ref_val and 15 <= ref_val <= 25:
                        batch_name = values[0].replace('Mean ', 'REF ')
                        ref_data.append({
                            'Product': 'REF',
                            'Batch': batch_name,
                            'Mean_Value': ref_val,
                            'Log_Mean': np.log(ref_val)
                        })
                    
                    if test_val and 15 <= test_val <= 25:
                        batch_name = values[0].replace('Mean ', 'TEST ')
                        test_data.append({
                            'Product': 'TEST',
                            'Batch': batch_name,
                            'Mean_Value': test_val,
                            'Log_Mean': np.log(test_val)
                        })
            except:
                pass
    
    return pd.DataFrame(ref_data), pd.DataFrame(test_data)

def calculate_comprehensive_pbe(ref_df, test_df):
    """Calculate comprehensive PBE analysis"""
    if ref_df is None or test_df is None or ref_df.empty or test_df.empty:
        print("Insufficient data for PBE analysis")
        return None
    
    print("\n=== COMPREHENSIVE POPULATION BIOEQUIVALENCE ANALYSIS ===")
    
    # Extract values
    ref_means = ref_df['Mean_Value'].values
    test_means = test_df['Mean_Value'].values
    ref_log_means = ref_df['Log_Mean'].values
    test_log_means = test_df['Log_Mean'].values
    
    # Basic statistics
    n_ref = len(ref_means)
    n_test = len(test_means)
    
    # Geometric means
    ref_geom_mean = np.exp(np.mean(ref_log_means))
    test_geom_mean = np.exp(np.mean(test_log_means))
    ratio = test_geom_mean / ref_geom_mean
    log_ratio = np.log(ratio)
    
    # Variances
    ref_var = np.var(ref_log_means, ddof=1) if n_ref > 1 else 0
    test_var = np.var(test_log_means, ddof=1) if n_test > 1 else 0
    
    # Within-subject variance (pooled)
    if n_ref > 1 and n_test > 1:
        pooled_var = ((n_ref - 1) * ref_var + (n_test - 1) * test_var) / (n_ref + n_test - 2)
        df = n_ref + n_test - 2
    else:
        pooled_var = max(ref_var, test_var)
        df = max(n_ref, n_test) - 1
    
    # Standard error
    se_ratio = np.sqrt(pooled_var * (1/n_ref + 1/n_test))
    
    # Confidence intervals
    t_90 = t.ppf(0.95, df)  # 90% CI
    t_95 = t.ppf(0.975, df)  # 95% CI
    
    ci_90_lower = np.exp(log_ratio - t_90 * se_ratio)
    ci_90_upper = np.exp(log_ratio + t_90 * se_ratio)
    ci_95_lower = np.exp(log_ratio - t_95 * se_ratio)
    ci_95_upper = np.exp(log_ratio + t_95 * se_ratio)
    
    # PBE Analysis
    # PBE criterion: θ = (μT - μR)² + σ²TT - σ²TR
    mean_diff_sq = (np.mean(test_log_means) - np.mean(ref_log_means))**2
    pbe_criterion = mean_diff_sq + test_var - ref_var
    pbe_constant = (np.log(1.25))**2  # 25% limit
    
    # TOST Analysis
    theta_limit = np.log(1.25)
    t1 = (log_ratio - theta_limit) / se_ratio
    t2 = (log_ratio + theta_limit) / se_ratio
    p1 = t.cdf(t1, df)
    p2 = 1 - t.cdf(t2, df)
    tost_p_value = max(p1, p2)
    
    # Power analysis (post-hoc)
    effect_size = abs(log_ratio) / np.sqrt(pooled_var)
    
    results = {
        'Sample_Size_Ref': n_ref,
        'Sample_Size_Test': n_test,
        'Reference_Arithmetic_Mean': np.mean(ref_means),
        'Test_Arithmetic_Mean': np.mean(test_means),
        'Reference_Geometric_Mean': ref_geom_mean,
        'Test_Geometric_Mean': test_geom_mean,
        'Geometric_Mean_Ratio': ratio,
        'Ratio_Percent': ratio * 100,
        'Log_Ratio': log_ratio,
        'Ratio_90_CI_Lower': ci_90_lower,
        'Ratio_90_CI_Upper': ci_90_upper,
        'Ratio_95_CI_Lower': ci_95_lower,
        'Ratio_95_CI_Upper': ci_95_upper,
        'Reference_CV_Percent': (np.sqrt(np.exp(ref_var) - 1) * 100) if ref_var > 0 else 0,
        'Test_CV_Percent': (np.sqrt(np.exp(test_var) - 1) * 100) if test_var > 0 else 0,
        'Reference_Variance': ref_var,
        'Test_Variance': test_var,
        'Pooled_Variance': pooled_var,
        'Standard_Error': se_ratio,
        'Degrees_Freedom': df,
        'PBE_Criterion': pbe_criterion,
        'PBE_Constant': pbe_constant,
        'PBE_Pass': pbe_criterion <= pbe_constant,
        'TOST_T1_Statistic': t1,
        'TOST_T2_Statistic': t2,
        'TOST_P_Value': tost_p_value,
        'TOST_Pass': tost_p_value <= 0.05,
        'ABE_Pass_80_125': (ci_90_lower >= 0.8) and (ci_90_upper <= 1.25),
        'ABE_Pass_90_111': (ci_90_lower >= 0.9) and (ci_90_upper <= 1.111),
        'Effect_Size': effect_size,
        'Bioequivalent': ((ci_90_lower >= 0.8) and (ci_90_upper <= 1.25)) or (pbe_criterion <= pbe_constant)
    }
    
    return results

def generate_comprehensive_report(results, ref_df, test_df):
    """Generate comprehensive bioequivalence report"""
    if results is None:
        return
    
    print("\n" + "="*80)
    print("COMPREHENSIVE POPULATION BIOEQUIVALENCE ANALYSIS REPORT")
    print("="*80)
    
    print(f"\n📋 STUDY DESIGN:")
    print(f"   Reference Batches: {results['Sample_Size_Ref']}")
    print(f"   Test Batches:      {results['Sample_Size_Test']}")
    print(f"   Total Observations: {results['Sample_Size_Ref'] + results['Sample_Size_Test']}")
    
    print(f"\n📊 DESCRIPTIVE STATISTICS:")
    print(f"   Reference Arithmetic Mean: {results['Reference_Arithmetic_Mean']:.4f}")
    print(f"   Test Arithmetic Mean:      {results['Test_Arithmetic_Mean']:.4f}")
    print(f"   Reference Geometric Mean:  {results['Reference_Geometric_Mean']:.4f}")
    print(f"   Test Geometric Mean:       {results['Test_Geometric_Mean']:.4f}")
    print(f"   Reference CV%:             {results['Reference_CV_Percent']:.2f}%")
    print(f"   Test CV%:                  {results['Test_CV_Percent']:.2f}%")
    
    print(f"\n🎯 BIOEQUIVALENCE ASSESSMENT:")
    print(f"   Geometric Mean Ratio:      {results['Geometric_Mean_Ratio']:.4f} ({results['Ratio_Percent']:.2f}%)")
    print(f"   90% Confidence Interval:   [{results['Ratio_90_CI_Lower']:.4f}, {results['Ratio_90_CI_Upper']:.4f}]")
    print(f"   95% Confidence Interval:   [{results['Ratio_95_CI_Lower']:.4f}, {results['Ratio_95_CI_Upper']:.4f}]")
    
    print(f"\n⚖️ AVERAGE BIOEQUIVALENCE (ABE):")
    print(f"   80-125% Criterion: {'✅ PASS' if results['ABE_Pass_80_125'] else '❌ FAIL'}")
    print(f"   90-111% Criterion: {'✅ PASS' if results['ABE_Pass_90_111'] else '❌ FAIL'}")
    
    print(f"\n🔬 POPULATION BIOEQUIVALENCE (PBE):")
    print(f"   PBE Criterion:     {results['PBE_Criterion']:.6f}")
    print(f"   PBE Constant:      {results['PBE_Constant']:.6f}")
    print(f"   PBE Assessment:    {'✅ PASS' if results['PBE_Pass'] else '❌ FAIL'}")
    
    print(f"\n📋 TOST ANALYSIS:")
    print(f"   TOST P-value:      {results['TOST_P_Value']:.6f}")
    print(f"   TOST Assessment:   {'✅ PASS' if results['TOST_Pass'] else '❌ FAIL'}")
    print(f"   T1 Statistic:      {results['TOST_T1_Statistic']:.4f}")
    print(f"   T2 Statistic:      {results['TOST_T2_Statistic']:.4f}")
    
    print(f"\n📈 STATISTICAL PARAMETERS:")
    print(f"   Degrees of Freedom: {results['Degrees_Freedom']}")
    print(f"   Standard Error:     {results['Standard_Error']:.6f}")
    print(f"   Effect Size:        {results['Effect_Size']:.4f}")
    
    print(f"\n🏆 FINAL CONCLUSION:")
    conclusion = "✅ BIOEQUIVALENT" if results['Bioequivalent'] else "❌ NOT BIOEQUIVALENT"
    print(f"   Overall Assessment: {conclusion}")
    
    if results['Bioequivalent']:
        print(f"   The test product demonstrates bioequivalence to the reference product.")
    else:
        print(f"   The test product does not demonstrate bioequivalence to the reference product.")
        print(f"   Consider reformulation or additional studies.")

if __name__ == "__main__":
    # Change to the correct directory
    os.chdir(r'c:\Users\<USER>\Downloads\suhass')
    
    # Extract refined data
    ref_df, test_df = extract_refined_data()
    
    # Calculate comprehensive PBE
    results = calculate_comprehensive_pbe(ref_df, test_df)
    
    # Generate report
    generate_comprehensive_report(results, ref_df, test_df)
    
    # Save all results
    if results and ref_df is not None and test_df is not None:
        # Save cleaned data
        combined_df = pd.concat([ref_df, test_df], ignore_index=True)
        combined_df.to_csv('pbe_final_data.csv', index=False)
        
        # Save results
        results_df = pd.DataFrame([results])
        results_df.to_csv('pbe_comprehensive_results.csv', index=False)
        
        print(f"\n💾 FILES SAVED:")
        print(f"   📄 pbe_final_data.csv - Final cleaned dataset")
        print(f"   📊 pbe_comprehensive_results.csv - Complete analysis results")
        
        print(f"\n🔧 JASP ANALYSIS INSTRUCTIONS:")
        print(f"   1. Download JASP: https://jasp-stats.org/download/")
        print(f"   2. Open 'pbe_final_data.csv' in JASP")
        print(f"   3. For TOST: T-Tests → Independent Samples T-Test")
        print(f"   4. For ANOVA: ANOVA → One Way ANOVA")
        print(f"   5. Set Product as Factor, Mean_Value as Dependent Variable")
        
        print(f"\n📋 REGULATORY FILING SUMMARY:")
        print(f"   Study demonstrates {'bioequivalence' if results['Bioequivalent'] else 'lack of bioequivalence'}")
        print(f"   90% CI: [{results['Ratio_90_CI_Lower']:.4f}, {results['Ratio_90_CI_Upper']:.4f}]")
        print(f"   Geometric Mean Ratio: {results['Ratio_Percent']:.2f}%")
        print(f"   PBE Criterion: {'Passed' if results['PBE_Pass'] else 'Failed'}")
        print(f"   TOST P-value: {results['TOST_P_Value']:.6f}")
